#include "GoogolCncInterface.h"
#include "tools/GoogolToolManager.h"

// 添加标准库包含
#include <algorithm>  // 用于 std::min, std::max
#include <atomic>
#include <chrono>
#include <cmath>    // 用于 std::abs
#include <codecvt>  // For codecvt_utf8
#include <cstring>  // 用于 strncpy, strlen
#include <functional>
#include <iomanip>   // For std::setw, std::left if needed for formatting
#include <iostream>  // 用于可能的调试输出
#include <limits>    // 用于 numeric_limits
#include <locale>    // For wstring_convert
#include <map>
#include <mutex>  // 可能需要用于线程安全
#include <stdexcept>
#include <string>
#include <thread>
#include <vector>  // Added for std::vector

#include "BaseCtrl.h"  // 包含底层状态、点动、回零功能
#include "CncCodeCompileEx.h"
#include "CncGlobalParamDll.h"     // 用于 GTC_... 访问共享内存的函数
#include "CncParamRWDll.h"         // 用于 GTCR_... 访问参数的函数
#include "CncWorkerThread.h"       // 添加CNC工作线程头文件
#include "CompilerWorkerThread.h"  // 添加编译线程头文件
#include "DllVersion.h"            // 用于 getSdkVersion
#include "ErrorCode.h"
#include "IAppLogger.h"
#include "ICncEventSystem.h"
#include "NCDll.h"
#include "PLCDLL.h"
#include "ParaDef.h"     // Googol 核心数据结构
#include "WarningDll.h"  // 包含报警功能
#include "gxn.h"

// 如果头文件中未直接提供，则定义使用的常量
#ifndef AXIS_NUM
#define AXIS_NUM 40  // 根据 Googol SDK 定义 (检查 GlobalDef_Linux.h 或 ParaDef.h)
#endif
constexpr short GoogolCncInterface::MAX_CHANNEL_NUM;  // 定义头文件中声明的静态常量成员

// --- 辅助映射和常量 ---

// 匿名命名空间中的 axisToGoogolIndex 和 mapAxisToIndex 将被移除

// 修改: 移除定义前的 static 关键字
std::string GoogolCncInterface::preparePath(const std::string& base, const std::string& leaf) {
    std::string full_path = base;
    if (!base.empty() && base.back() != '/' && base.back() != '\\') {
        full_path += '/';
    }
    full_path += leaf;
    return full_path;
}

// --- 构造函数 & 析构函数 ---

GoogolCncInterface::GoogolCncInterface()
    : m_isInitialized(false),
      m_lastError("接口尚未初始化"),
      m_loadedAxesCount(0),
      m_loadedSpindlesCount(0),
      m_loadedChannelsCount(0),
      m_cncThread(nullptr),
      m_compilerThread(nullptr) {
    // 初始化配置设置器
    initConfigurationSetters();

    // 创建刀具管理器
    m_toolManager = std::make_unique<GoogolToolManager>(*this);
}

GoogolCncInterface::~GoogolCncInterface() {
    HW_LOG_DEBUG(m_logger, "析构函数");
    if (m_isInitialized) {
        HW_LOG_DEBUG(m_logger, "析构函数调用，接口已初始化，执行关闭流程。");
        shutdown();
    }
    HW_LOG_DEBUG(m_logger, "析构函数完毕");
}

// --- 生命周期方法 ---

ErrorCode GoogolCncInterface::initialize(const CncInitializationParams& params) {
    m_logger = params.logger;
    HW_LOG_INFO(m_logger, "初始化开始。");
    if (m_isInitialized) {
        HW_LOG_INFO(m_logger, "接口已经初始化。");
        return ErrorCode::Success;
    }

    // 从参数结构体中提取配置路径
    m_configPath = params.configPath;
    if (m_configPath.empty()) {
        HW_LOG_ERROR(m_logger, "configPath 为空。");
        return ErrorCode::InvalidParam;
    }

    if (!params.writablePath.empty()) {
        HW_LOG_INFO(m_logger, "可写目录路径: %s", params.writablePath.c_str());
        m_writablePath = params.writablePath;
    } else {
        m_writablePath = "./cnc_config";
    }

    // TODO: 在这里可以使用 params.logger 来设置日志输出
    // TODO: 在这里可以使用 params.writablePath 来设置可写目录
    // TODO: 在这里可以使用 params.customParameters 来设置自定义参数

    initPlcKeyMap();
    initEventFlags();

    ErrorCode ec = ErrorCode::Success;
    std::string errorCfgFilePath, plcPath, macroPath;

    // 1. 初始化SDK路径 (包括GTCR_InitSetParaFilePath)
    ec = initSdkPaths(m_configPath, errorCfgFilePath, plcPath, macroPath);
    if (ec != ErrorCode::Success) return ec;

#if 0
    // 2. 创建/初始化共享内存并加载参数
    ec = initAndLoadSharedMemoryInternal(errorCfgFilePath);
    // FIXME: 这里需要处理错误
    // if (ec != ErrorCode::Success) return ec;

    // 3. 初始化配置
    {
        HW_LOG_INFO(m_logger, "getConfiguration: %s", m_writablePath.c_str());
        std::vector<ConfigCategory> rootConfigCategories;
        ec = getConfiguration(rootConfigCategories);
        if (ec != ErrorCode::Success) return ec;
    }

#else
    ec = initCncSystem(errorCfgFilePath);
    // FIXME: 这里需要处理错误
    // if (ec != ErrorCode::Success) return ec;
    ec = ErrorCode::Success;
#endif

    //// 2. 初始化调试模块
    // ec = initDebugModuleInternal(errorCfgFilePath);
    // if (ec != ErrorCode::Success) return ec;

    // 4. 初始化NC
    if (ec == ErrorCode::Success) {
        ec = initNcInternal();
    }
    // FIXME: 这里需要处理错误
    // if (ec != ErrorCode::Success) return ec;

    // 5. 初始化PLC
    if (ec == ErrorCode::Success) {
        ec = initPlcInternal(plcPath);
    }
    // FIXME: 这里需要处理错误
    // if (ec != ErrorCode::Success) return ec;

    // 5. 初始化译码
    if (ec == ErrorCode::Success) {
        ec = initCompilerInternal(macroPath);
    }

    // 6. 初始化MDI和宏
    if (ec == ErrorCode::Success) {
        ec = initMDI();
    }
    if (ec == ErrorCode::Success) {
        ec = initMacroFile();
    }

    // FIXME: 这里需要处理错误
    // if (ec != ErrorCode::Success) return ec;

    // 7. 创建并启动CNC和编译线程 (之前是步骤9)
    if (ec == ErrorCode::Success) {
        ec = startWorkerThreadsInternal();
    }
    // FIXME: 这里需要处理错误
    // if (ec != ErrorCode::Success) return ec;
    startEventThread();

    m_isInitialized = true;
    m_lastError = "";  // 清除之前的错误信息
    HW_LOG_INFO(m_logger, "初始化成功完成。");

    // 初始化刀具数据
    m_toolManager->initialize();
    HW_LOG_INFO(m_logger, "刀具数据初始化完成。");

    // !!!输出宏绑定结果!!!
    debugMacroBindings();

    return this->initSystemConfig(m_systemConfig);
}

ErrorCode GoogolCncInterface::shutdown() {
    HW_LOG_INFO(m_logger, "关闭序列开始。");
    if (!m_isInitialized) {
        HW_LOG_INFO(m_logger, "接口未初始化，无需操作。");
        m_lastError = "接口未初始化，无需关闭";
        return ErrorCode::NotInitialized;
    }

    // 0. 延时断电
    // - MC_GPO(12): 通用输出
    // - 9: 输出 IO 的索引
    // - 1: 表示高电平
    // - 10000: 中断周期
    GTC_SetDoBitReverse(12, 9, 1, 10000);

    // 1. 停止并删除线程
    if (m_cncThread) {
        HW_LOG_INFO(m_logger, "停止 CncWorkerThread。");
        m_cncThread->stop();  // 使用 stop()
        // m_cncThread->wait();    // 如果 StoppableThread::stopThread 是异步的，可能需要等待
        HW_LOG_DEBUG(m_logger, "删除 CncWorkerThread。");
        delete m_cncThread;
        m_cncThread = nullptr;
    }

    if (m_compilerThread) {
        HW_LOG_INFO(m_logger, "停止 CompilerWorkerThread。");
        m_compilerThread->stop();  // 使用 stop()
        // m_compilerThread->wait(); // 如果 StoppableThread::stopThread 是异步的，可能需要等待
        HW_LOG_DEBUG(m_logger, "删除 CompilerWorkerThread。");
        delete m_compilerThread;
        m_compilerThread = nullptr;
    }
    HW_LOG_INFO(m_logger, "工作线程已停止并删除。");

    stopEventThread();

    // 2. 调用 Googol SDK 的反初始化/释放函数
    HW_LOG_INFO(m_logger, "执行SDK反初始化。");
    // 顺序与 CncManager::exitSystem() 类似
    HW_LOG_INFO(m_logger, "调用 GTC_StopPLCTimer。");
    GTC_StopPLCTimer();
    // GTC_PLCReleaseUICom(); // 根据实际需要和SDK文档决定是否调用
    HW_LOG_INFO(m_logger, "调用 GTC_DeletePLC。");
    GTC_DeletePLC();
    HW_LOG_INFO(m_logger, "调用 GTC_ReleaseNC。");
    GTC_ReleaseNC();
    HW_LOG_INFO(m_logger, "调用 GTCP_ReleaseCompiler。");
    GTCP_ReleaseCompiler();

    short rtn = 1;
    HW_LOG_INFO(m_logger, "释放共享内存。");
    // 循环释放共享内存，直到返回值表明已无内存可释放或发生错误
    int release_attempts = 0;
    while (rtn > 0) {
        release_attempts++;
        rtn = GTC_ReleaseShareMem();
        HW_LOG_DEBUG(m_logger, "GTC_ReleaseShareMem 尝试 %d 返回: %d", release_attempts, rtn);
        if (rtn < 0) {
            HW_LOG_ERROR(m_logger, "GTC_ReleaseShareMem 在尝试 %d 次后失败", release_attempts);
            // 即使发生错误，也应继续尝试其他清理步骤，但标记初始化失败
            break;
        }
        if (release_attempts > 10 && rtn > 0) {  // Safety break for unexpected persistent positive return
            HW_LOG_ERROR(m_logger, "警告：GTC_ReleaseShareMem 在 %d 次尝试后仍返回正值。中断循环。", release_attempts);
            break;
        }
    }
    HW_LOG_INFO(m_logger, "共享内存释放过程完成。");

    m_isInitialized = false;
    m_lastError = "接口已关闭";
    HW_LOG_INFO(m_logger, "关闭序列完成。");
    return ErrorCode::Success;
}

bool GoogolCncInterface::isInitialized() const { return m_isInitialized; }

// --- 事件处理方法 ---
ErrorCode GoogolCncInterface::registerEventListener(ICncEventListener* listener) {
    if (!listener) {
        m_lastError = "registerEventListener 错误: 监听器指针为空。";
        HW_LOG_ERROR(m_logger, "%s", m_lastError.c_str());
        return ErrorCode::InvalidParam;
    }
    std::lock_guard<std::mutex> lock(m_eventListenerMutex);
    // 避免重复添加
    if (std::find(m_eventListeners.begin(), m_eventListeners.end(), listener) == m_eventListeners.end()) {
        m_eventListeners.push_back(listener);
        HW_LOG_INFO(m_logger, "事件监听器已注册。");
    } else {
        HW_LOG_INFO(m_logger, "尝试注册一个已存在的事件监听器。");
    }
    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::unregisterEventListener(ICncEventListener* listener) {
    if (!listener) {
        m_lastError = "unregisterEventListener 错误: 监听器指针为空。";
        HW_LOG_ERROR(m_logger, "%s", m_lastError.c_str());
        return ErrorCode::InvalidParam;
    }
    std::lock_guard<std::mutex> lock(m_eventListenerMutex);
    auto it = std::find(m_eventListeners.begin(), m_eventListeners.end(), listener);
    if (it != m_eventListeners.end()) {
        m_eventListeners.erase(it);
        HW_LOG_INFO(m_logger, "事件监听器已注销。");
        return ErrorCode::Success;
    } else {
        m_lastError = "unregisterEventListener 错误: 尝试注销一个未注册的监听器。";
        HW_LOG_ERROR(m_logger, "%s", m_lastError.c_str());
        return ErrorCode::OperationFailed;  // 或者 ErrorCode::InvalidParam
    }
}

void GoogolCncInterface::notifyEventListeners(const CncEvent& event) {
    std::vector<ICncEventListener*> listeners_copy;
    {
        std::lock_guard<std::mutex> lock(m_eventListenerMutex);
        listeners_copy = m_eventListeners;  // 复制列表以在锁外调用回调
    }

    if (listeners_copy.empty()) {
        return;
    }

    for (ICncEventListener* listener : listeners_copy) {
        if (listener) {
            try {
                listener->onCncEvent(event);
            } catch (const std::exception& e) {
                HW_LOG_ERROR(m_logger, "事件监听器 onCncEvent 抛出异常: %s", e.what());
            } catch (...) {
                HW_LOG_ERROR(m_logger, "事件监听器 onCncEvent 抛出未知异常。");
            }
        }
    }
}

// --- UI模态模式通知方法 ---
ErrorCode GoogolCncInterface::enterModalMode(UiModalType modalType, const std::string& modalId) {
    HW_LOG_INFO(m_logger, "进入模态模式: %s", modalId.c_str());
    setPlcDataXBit(150, 0, 1);
    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::exitModalMode(UiModalType modalType, const std::string& modalId) {
    HW_LOG_INFO(m_logger, "退出模态模式: %s", modalId.c_str());
    setPlcDataXBit(150, 0, 0);
    return ErrorCode::Success;
}

// --- 获取最后错误 ---

ErrorCode GoogolCncInterface::getLastError(std::string& errorMsg) {
    errorMsg = m_lastError;
    return ErrorCode::Success;
}

// --- 内部辅助方法 ---

ErrorCode GoogolCncInterface::checkInitializedAndPointers() {
    if (!m_isInitialized) {
        m_lastError = "接口未初始化";
        return ErrorCode::NotInitialized;
    }

    // 检查关键指针是否有效
    if (m_ncOutPtr.m_shm8Ptr == NULL || m_sysParamPtr.m_shm8Ptr == NULL || m_plcGPara == nullptr) {
        m_lastError = "关键系统指针无效";
        return ErrorCode::SdkError;
    }

    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::handleGoogolReturnCode(short googolRetCode, const std::string& operationContext) {
    if (googolRetCode == 0) {
        return ErrorCode::Success;
    }

    // 格式化错误信息，包含操作上下文和Googol返回码
    m_lastError = operationContext + " 失败: Googol错误码 " + std::to_string(googolRetCode);

    // 映射Googol错误码到通用错误码
    switch (googolRetCode) {
            // 常见的Googol错误码映射
            // 例如:
            // case -1: return ErrorCode::InvalidParam;
            // case -2: return ErrorCode::SdkError;
            // 等等...

        default:
            return ErrorCode::SdkError;  // 默认错误类型
    }
}

// --- 工作模式映射辅助函数 ---

// 将Googol工作模式代码映射到OperatingMode枚举
OperatingMode GoogolCncInterface::mapGoogolWorkModeToEnum(unsigned short googolMode) {
    // 此处需要参考Googol SDK文档中工作模式的定义
    switch (googolMode) {
        case 0:
            return OperatingMode::Auto;  // 假设0=自动模式
        case 1:
            return OperatingMode::MDI;  // 假设1=MDI模式
        case 2:
            return OperatingMode::Manual;  // 假设2=手动模式
        case 3:
            return OperatingMode::Manual;  // 假设3=手轮模式，映射到Manual
        case 4:
            return OperatingMode::Home;  // 假设4=回零模式
        // 其他模式...
        default:
            return OperatingMode::Unknown;  // 未知模式
    }
}

// 将OperatingMode枚举映射到Googol工作模式代码
unsigned short GoogolCncInterface::mapEnumToGoogolWorkMode(OperatingMode mode) {
    // 此处需要参考Googol SDK文档中工作模式的定义
    switch (mode) {
        case OperatingMode::Auto:
            return 0;  // 假设自动模式=0
        case OperatingMode::MDI:
            return 1;  // 假设MDI模式=1
        case OperatingMode::Manual:
            return 2;  // 假设手动模式=2
        case OperatingMode::Edit:
            return 5;  // 假设编辑模式=5
        case OperatingMode::Home:
            return 4;  // 假设回零模式=4
        // 其他模式...
        default:
            return 0;  // 默认返回自动模式或其他安全模式
    }
}

// --- 辅助函数实现 ---

std::string WcharToStdString(const wchar_t* wstr) {
    if (!wstr) return "";

    // 计算需要的缓冲区大小（UTF-8通常是宽字符的1-4倍）
    size_t len = 0;
    while (wstr[len]) len++;  // 找到宽字符串的长度

    // 分配足够大的缓冲区用于UTF-8字符串
    std::string result;
    result.resize(len * 4);  // 确保足够空间

#ifdef _WIN32
    // Windows平台
    int size = WideCharToMultiByte(CP_UTF8, 0, wstr, -1, &result[0], result.size(), NULL, NULL);
    if (size > 0) {
        result.resize(size - 1);  // 减去NULL终止符
    } else {
        return "";  // 转换失败
    }
#else
    // Linux/Unix平台
    size_t size = wcstombs(&result[0], wstr, result.size());
    if (size != (size_t)-1) {
        result.resize(size);  // 调整大小至实际字节数
    } else {
        return "";  // 转换失败
    }
#endif

    return result;
}

// --- 新的私有辅助函数实现 ---

ErrorCode GoogolCncInterface::initSdkPaths(const std::string& configPath, std::string& outErrorCfgPath,
                                           std::string& outPlcPath, std::string& outMacroPath) {
    HW_LOG_DEBUG(m_logger, "initSdkPaths 开始。");
    std::string sysPath_str = preparePath(configPath, "SYS");
    HW_LOG_INFO(m_logger, "设置SYS文件路径为: %s", sysPath_str.c_str());

    // 转换为可写 char*
    std::vector<char> sysPath_vec(sysPath_str.begin(), sysPath_str.end());
    sysPath_vec.push_back('\0');
    // 初始化设置参数文件路径
    short ret = GTCR_InitSetParaFilePath(sysPath_vec.data());

    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTCR_InitSetParaFilePath 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTCR_InitSetParaFilePath");
    }
    HW_LOG_INFO(m_logger, "GTCR_InitSetParaFilePath 成功，路径: %s", sysPath_str.c_str());

    outErrorCfgPath = preparePath(configPath, "NcConfig/error_cfg.ini");
    outPlcPath = preparePath(configPath, "PLC");
    outMacroPath = preparePath(configPath, "sFile");
    HW_LOG_DEBUG(m_logger, "initSdkPaths 完成。 ErrorCfgPath: %s, PlcPath: %s, MacroPath: %s", outErrorCfgPath.c_str(),
                 outPlcPath.c_str(), outMacroPath.c_str());
    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::initDebugModuleInternal(const std::string& errorCfgFilePath_str) {
    HW_LOG_DEBUG(m_logger, "initDebugModuleInternal 开始，配置文件: %s", errorCfgFilePath_str.c_str());
    // 初始化报警模块
    short ret = GTC_InitWarningModule();
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_InitWarningModule 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_InitWarningModule");
    }
    HW_LOG_INFO(m_logger, "GTC_InitWarningModule 成功。");

    // 转换为可写 char*
    std::vector<char> errorCfgFilePath_vec(errorCfgFilePath_str.begin(), errorCfgFilePath_str.end());
    errorCfgFilePath_vec.push_back('\0');
    // 加载报警文件
    ret = GTC_UpdateWarningInfo(errorCfgFilePath_vec.data());

    // 复位报警数据
    ret = GTC_ResetWarningData();
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_ResetWarningData 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_ResetWarningData");
    }
    HW_LOG_INFO(m_logger, "GTC_ResetWarningData 成功。");

    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_UpdateWarningInfo 失败，文件: %s，返回码: %d", errorCfgFilePath_str.c_str(), ret);
        return handleGoogolReturnCode(ret, "GTC_UpdateWarningInfo");
    }
    HW_LOG_INFO(m_logger, "GTC_UpdateWarningInfo 成功。");

    // 打开MSG功能
    ret = GTC_InitMsgModule();
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_InitMsgModule 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_InitMsgModule");
    }
    HW_LOG_INFO(m_logger, "GTC_InitMsgModule 成功。");

    // 打开调试功能
    // ret = GTC_InitDebugMsg(m_sysParamPtr.m_shm8Ptr->m_bLogDubug);
    ret = GTC_InitDebugMsg();
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_InitDebugMsg 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_InitDebugMsg");
    }
    HW_LOG_INFO(m_logger, "GTC_InitDebugMsg 成功。");

    GTC_EnableDebugCtrl(true);
    HW_LOG_INFO(m_logger, "GTC_EnableDebugCtrl 已调用。");
    HW_LOG_DEBUG(m_logger, "initDebugModuleInternal 完成。");
    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::initAndLoadSharedMemoryInternal(const std::string& errorCfgFilePath) {
    HW_LOG_DEBUG(m_logger, "initAndLoadSharedMemoryInternal 开始。");
    short ret = GTC_InitShareMemParamPtr();
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_InitShareMemParamPtr 失败，SDK返回码: %d. 继续执行可能导致问题。", ret);
        return handleGoogolReturnCode(ret, "GTC_InitShareMemParamPtr");
    }
    HW_LOG_INFO(m_logger, "GTC_InitShareMemParamPtr 调用完成。");

    // 2. 初始化调试模块
    if (initDebugModuleInternal(errorCfgFilePath) != ErrorCode::Success) {
        HW_LOG_ERROR(m_logger, "initDebugModuleInternal 失败");
        return ErrorCode::InternalError;
    }

    // //系统参数
    ret = GTC_GetPtrSysPara(m_sysParamPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrSysPara 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrSysPara");
    }
    HW_LOG_INFO(m_logger, "GTC_GetPtrSysPara 成功。");
    ret = GTCR_LoadSysPara(m_sysParamPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTCR_LoadSysPara 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTCR_LoadSysPara");
    }
    HW_LOG_INFO(m_logger, "GTCR_LoadSysPara 成功。");

    if (!m_sysParamPtr.m_shm32Ptr) {
        HW_LOG_ERROR(m_logger, "m_sysParamPtr.m_shm32Ptr 在 GTC_GetPtrSysPara 和 GTCR_LoadSysPara 后为空。");
        return ErrorCode::InternalError;
    }
    HW_LOG_INFO(m_logger, "系统参数共享内存指针 (m_shm32Ptr) 有效。");

    // m_loadedAxesCount = m_sysParamPtr.m_shm32Ptr->m_SysAxisNum;         // 直线轴数
    // m_loadedSpindlesCount = m_sysParamPtr.m_shm32Ptr->m_SysSpindleNum;  // 主轴数
    m_loadedChannelsCount = m_sysParamPtr.m_shm32Ptr->m_SysChannelNum;
    int totalAxesCount = m_sysParamPtr.m_shm32Ptr->m_SysAxisNum + m_sysParamPtr.m_shm32Ptr->m_SysSpindleNum;

    // NC输出公共参数
    ret = GTC_GetPtrNcOut(m_ncOutPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrNcOut 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrNcOut");
    }

    // HMI输出参数
    ret = GTC_GetPtrHmiOut(m_hmiOutPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrHmiOut 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrHmiOut");
    }

    // PLC 输出参数
    ret = GTC_GetPtrPlcOut(m_plcOutPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrPlcOut 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrPlcOut");
    }

    // 轴配置
    ret = GTC_GetPtrAxesConfig(m_axesCfgPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrAxesConfig 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrAxesConfig");
    }
    HW_LOG_INFO(m_logger, "GTC_GetPtrAxesConfig 成功。");

    ret = GTCR_LoadAxesConfig(m_axesCfgPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTCR_LoadAxesConfig 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTCR_LoadAxesConfig");
    }
    HW_LOG_INFO(m_logger, "GTCR_LoadAxesConfig 成功。");

    // 坐标参数
    ret = GTC_GetPtrCoordPara(m_coordParamPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrCoordPara 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrCoordPara");
    }
    HW_LOG_INFO(m_logger, "GTC_GetPtrCoordPara 成功。");
    ret = GTCR_LoadCoordPara(m_coordParamPtr);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTCR_LoadCoordPara 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTCR_LoadCoordPara");
    }
    HW_LOG_INFO(m_logger, "GTCR_LoadCoordPara 成功。");

    for (int i = 0; i < totalAxesCount; ++i) {
        // 轴控制参数
        ret = GTC_GetPtrAxisPara(m_axisParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrAxisPara 获取轴/主轴索引 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrAxisPara");
        }
        ret = GTC_GetPtrScrewPitchComp(m_axisScrewPitchCompPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrScrewPitchComp 获取轴/主轴索引 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrScrewPitchComp");
        }
    }

    for (int i = 0; i < totalAxesCount; ++i) {
        // 轴控制参数
        ret = GTCR_LoadAxisPara(m_axisParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadAxisPara 加载轴/主轴索引 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadAxisPara");
        }
        ret = GTCR_LoadScrewPitchComp(m_axisScrewPitchCompPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadScrewPitchComp 加载轴/主轴索引 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadScrewPitchComp");
        }
    }
    HW_LOG_INFO(m_logger, "完成加载轴/主轴参数。");

    HW_LOG_INFO(m_logger, "加载 %d 个通道的参数 (SysChannelNum)。", m_loadedChannelsCount);
    for (int i = 0; i < MAX_CHANNEL_NUM; ++i) {
        // 运动参数
        ret = GTC_GetPtrMotionPara(m_motionParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrMotionPara 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrMotionPara");
        }
        // ... (repeat for GCode, G5_1, Tool, MacroPublic, MacroSetting parameters) ...
        // //工艺参数
        ret = GTC_GetPtrGCodePara(m_gcodeParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrGCodePara 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrGCodePara");
        }
        // 高速高精参数
        ret = GTC_GetPtrG5_1Para(m_tg51ParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrG5_1Para 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrG5_1Para");
        }
        // 刀具参数
        ret = GTC_GetPtrToolPara(m_toolsParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrToolPara 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrToolPara");
        }
        // 宏变量
        ret = GTC_GetPtrMacroPublicPara(m_macroPublicParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrMacroPublicPara 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrMacroPublicPara");
        }
        // 宏设置参数
        ret = GTC_GetPtrMacroSettingPara(m_macroSettingParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrMacroSettingPara 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrMacroSettingPara");
        }
        // NC 通道输出参数
        ret = GTC_GetPtrNcChannelOut(m_ncChannelOutPtrArr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTC_GetPtrNcChannelOut 获取通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTC_GetPtrNcChannelOut");
        }
    }

    // PLC:X\Y\F\G\D变量
    ret = GTC_GetPtrPlcPara(m_plcPara);
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_GetPtrPlcPara 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_GetPtrPlcPara");
    }
    // PLC：F变量参数
    m_plcFPara = (NC2PLC_PARA_F*)m_plcPara.m_shm16Ptr->F;
    // PLC：G变量参数
    m_plcGPara = (PLC2NC_PARA_G*)m_plcPara.m_shm16Ptr->G;

    for (int i = 0; i < m_loadedChannelsCount; ++i) {
        // 运动参数
        ret = GTCR_LoadMotionPara(m_motionParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadMotionPara 加载通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadMotionPara");
        }
        // ... (repeat for GCode, G5_1, Tool, MacroPublic, MacroSetting parameters) ...
        // //工艺参数
        ret = GTCR_LoadGCodePara(m_gcodeParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadGCodePara 加载通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadGCodePara");
        }
        ret = GTCR_LoadG5_1Para(m_tg51ParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadG5_1Para 加载通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadG5_1Para");
        }
        // 刀具参数
        ret = GTCR_LoadToolPara(m_toolsParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadToolPara 加载通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadToolPara");
        }
        // 宏设置参数
        ret = GTCR_LoadMacroSettingPara(m_macroSettingParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadMacroSettingPara 加载通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadMacroSettingPara");
        }
        // Googol Lib Bug:
        // 程序的当前工作目录必须是程序所在的路径，否则初始出错！！！
        // 即：/opt/LatheCNC/LatheCNC这样执行程序会导致GTCR_LoadMacroPublicPara失败！！！
        // 正确的方式:/opt/LatheCNC/;./LatheCNC
        //  宏变量
        ret = GTCR_LoadMacroPublicPara(m_macroPublicParamPtr[i], i);
        if (ret != 0) {
            HW_LOG_ERROR(m_logger, "GTCR_LoadMacroPublicPara 加载通道 %d 失败，返回码: %d", i, ret);
            return handleGoogolReturnCode(ret, "GTCR_LoadMacroPublicPara");
        }
    }

    // printAxisConfig();
    // printAxisPara();

    HW_LOG_INFO(m_logger, "完成加载通道参数。");
    HW_LOG_DEBUG(m_logger, "initAndLoadSharedMemoryInternal 完成。");
    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::initNcInternal() {
    HW_LOG_DEBUG(m_logger, "initNcInternal 开始。");

    short ret = GTC_InitNC();
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_InitNC 失败，返回码: %d, 忽略错误，可能是配置错误！", ret);
        // 初始化失败，将配置初始化标志设置为false
        // GTC_InitNC 可能会修改配置，所以需要将配置初始化标志设置为false
        m_isConfigInitialized = false;
        // return handleGoogolReturnCode(ret, "GTC_InitNC");
    }

    HW_LOG_DEBUG(m_logger, "initNcInternal 完成。");
    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::initMDI() {
    HW_LOG_DEBUG(m_logger, "initMDI 开始");

    m_sLoadMDINcFile = QCoreApplication::applicationDirPath() + "/SYS/MDI.NC";

    QFile file(m_sLoadMDINcFile);
    if (!file.exists())  // 判断MDI文件是否存在，不存在则创建。平台不会帮忙创建
    {
        file.open(QIODevice::ReadWrite);
        file.close();
    }

    bool ret = Nc_LoadNcFile(m_sLoadMDINcFile, 0, MDI_COMPILE /* + TYPE_COMPILE_NUM*/);
    if (!ret) {
        HW_LOG_ERROR(m_logger, "GTCP_LoadNcFile 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTCP_LoadNcFile");
    }

    HW_LOG_DEBUG(m_logger, "initMDI 完成。");
    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::initMacroFile() {
    HW_LOG_DEBUG(m_logger, "initMacroFile 开始");

    m_sLoadMacroFile = QCoreApplication::applicationDirPath() + "/sFile/L9999.NC";

    QFile file(m_sLoadMacroFile);
    if (!file.exists()) {
        file.open(QIODevice::ReadWrite);
        file.close();
    }

    HW_LOG_DEBUG(m_logger, "m_sLoadMacroFile 完成。");
    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::initPlcInternal(const std::string& plcPath_str) {
    HW_LOG_DEBUG(m_logger, "initPlcInternal 开始，PLC路径: %s", plcPath_str.c_str());

    // 转换为可写 char*
    std::vector<char> plcPath_vec(plcPath_str.begin(), plcPath_str.end());
    plcPath_vec.push_back('\0');
    // PLC文件夹全路径
    short ret = GTC_SetPLCFilePath(plcPath_vec.data());

    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_SetPLCFilePath 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_SetPLCFilePath");
    }
    HW_LOG_INFO(m_logger, "GTC_SetPLCFilePath 成功。");

    // 初始化PLC模块
    ret = GTC_InitPLC();
    if (ret != 0) {
        HW_LOG_ERROR(m_logger, "GTC_InitPLC 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTC_InitPLC");
    }
    HW_LOG_INFO(m_logger, "GTC_InitPLC 成功。");

    // 注册PLC内部弹框回调函数
    // 使用lambda表达式注册PLC内部弹框回调函数，符合中文注释规范
    ret = GTC_RegisterPlcMsgBoxCallback([](PlcMsgBoxType type, const std::string& msg) -> PlcMsgBoxResult {
        std::cout << "=> PLC Message (" << static_cast<int>(type) << "): " << msg;
        return PlcMsgBoxResult::PMBR_OK;
    });
    if (!ret) {
        HW_LOG_ERROR(m_logger, "GTC_RegisterPlcMsgBoxCallback 失败");
        return ErrorCode::OperationFailed;
    }
    HW_LOG_INFO(m_logger, "GTC_RegisterPlcMsgBoxCallback 成功。");

    GTC_StartPLCTimer();
    HW_LOG_DEBUG(m_logger, "initPlcInternal 完成。");
    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::initCompilerInternal(const std::string& macroPath) {
    HW_LOG_DEBUG(m_logger, "initCompilerInternal 开始。");
    // 初始化译码器
    short ret = GTCP_InitCompiler();
    if (ret == 0) {
        HW_LOG_ERROR(m_logger, "GTCP_InitCompiler 失败，返回码: %d", ret);
        return handleGoogolReturnCode(ret, "GTCP_InitCompiler");
    }
    HW_LOG_DEBUG(m_logger, "GTCP_InitCompiler 成功。");

    // 设置子程序路径，用于加载宏程序（T.NCC, M06.NCC等）
    bool pathResult = GTCP_InitChildFilePath(const_cast<char*>(macroPath.c_str()), 0);
    if (!pathResult) {
        HW_LOG_ERROR(m_logger, "GTCP_InitChildFilePath 失败，路径: %s", macroPath.c_str());
        return ErrorCode::OperationFailed;
    }
    HW_LOG_INFO(m_logger, "子程序路径设置成功: %s", macroPath.c_str());

    HW_LOG_DEBUG(m_logger, "initCompilerInternal 完成。");
    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::startWorkerThreadsInternal() {
    HW_LOG_DEBUG(m_logger, "startWorkerThreadsInternal 开始。");
    if (m_cncThread) {
        HW_LOG_DEBUG(m_logger, "删除已存在的 CncWorkerThread 实例。");
        delete m_cncThread;
        m_cncThread = nullptr;
    }
    if (m_compilerThread) {
        HW_LOG_DEBUG(m_logger, "删除已存在的 CompilerWorkerThread 实例。");
        delete m_compilerThread;
        m_compilerThread = nullptr;
    }

    m_cncThread = new CncWorkerThread();
    if (m_cncThread) {
        HW_LOG_INFO(m_logger, "CncWorkerThread 已分配。正在启动...");
        m_cncThread->start();
    } else {
        HW_LOG_ERROR(m_logger, "分配 CncWorkerThread 失败。");
        return ErrorCode::InternalError;
    }

    m_compilerThread = new CompilerWorkerThread();
    if (m_compilerThread) {
        HW_LOG_INFO(m_logger, "CompilerWorkerThread 已分配。正在启动...");
        m_compilerThread->start();
    } else {
        HW_LOG_ERROR(m_logger, "分配 CompilerWorkerThread 失败。");
        if (m_cncThread) {
            HW_LOG_DEBUG(m_logger, "因 CompilerWorkerThread 分配失败，正在停止并删除 CncWorkerThread。");
            m_cncThread->stop();
            delete m_cncThread;
            m_cncThread = nullptr;
        }
        return ErrorCode::InternalError;
    }
    HW_LOG_DEBUG(m_logger, "startWorkerThreadsInternal 完成。");
    return ErrorCode::Success;
}

void GoogolCncInterface::startEventThread() {
    if (m_eventThread.joinable()) return;
    m_eventStopFlag.store(false);
    m_eventPausedFlag.store(false);  // 默认不暂停
    m_eventThread = std::thread(&GoogolCncInterface::eventThreadLoop, this);
}

void GoogolCncInterface::stopEventThread() {
    m_eventStopFlag.store(true);
    resumeEventThread();  // 如果处于暂停状态，需要唤醒它以检查 stopFlag
}

void GoogolCncInterface::waitEventThread() {
    if (m_eventThread.joinable()) {
        m_eventThread.join();
    }
}

void GoogolCncInterface::pauseEventThread() {
    m_eventPausedFlag.store(true);
    HW_LOG_INFO(m_logger, "CompilerWorkerThread 已暂停");
}

void GoogolCncInterface::resumeEventThread() {
    m_eventPausedFlag.store(false);
    HW_LOG_INFO(m_logger, "CompilerWorkerThread 已恢复");
}

bool GoogolCncInterface::isEventThreadPaused() const { return m_eventPausedFlag.load(); }

void GoogolCncInterface::initEventFlags() {
    for (size_t i = 0; i < m_chanProgramStatusArr.size(); ++i) {
        m_chanProgramStatusArr[i] = ProgramStatus{};
    }

    m_iSysStatus = CncSystemState::Idle;  // 记录系统状态
    m_opMode = OperatingMode::Auto;
}

void GoogolCncInterface::checkConnectionStatusEvent() {}

void GoogolCncInterface::checkProgramStatusChangedEvent() {
    // 检查程序状态是否发生变化
    for (size_t i = 0; i < m_ncChannelOutPtrArr.size(); ++i) {
        if (m_ncChannelOutPtrArr[i].m_shm32Ptr != nullptr) {
            CncSystemState state = m_currentSysStatusArr[i];
            ProgramStatus newStatus = m_chanProgramStatusArr[i];
            bool hasChanged = false;

            // 检查程序层级相关变化
            int32_t currentProgramNo = m_ncChannelOutPtrArr[i].m_shm32Ptr->NC_Status_FileNo;
            if (newStatus.currentProgramNo != currentProgramNo) {
                HW_LOG_DEBUG(m_logger, "通道 %zu 程序序号变化: %d -> %d", i, newStatus.currentProgramNo,
                             currentProgramNo);
                newStatus.currentProgramNo = currentProgramNo;
                hasChanged = true;
            }

            // 检查嵌套层级变化
            uint32_t nestingLevel = m_ncChannelOutPtrArr[i].m_shm32Ptr->NC_PrgLevel;
            if (newStatus.nestingLevel != nestingLevel) {
                HW_LOG_DEBUG(m_logger, "通道 %zu 程序嵌套层级变化: %d -> %d", i, newStatus.nestingLevel, nestingLevel);
                newStatus.nestingLevel = nestingLevel;
                hasChanged = true;
            }

            // 检查主程序行号变化 (从1开始，而NC_Status_Main_Line_NO从0开始)
            uint32_t currentBlock = m_ncChannelOutPtrArr[i].m_shm32Ptr->NC_Status_Main_Line_NO + 1;
            if (newStatus.currentBlock != currentBlock) {
                HW_LOG_DEBUG(m_logger, "通道 %zu 主程序行号变化: %d -> %d", i, newStatus.currentBlock, currentBlock);
                newStatus.currentBlock = currentBlock;
                hasChanged = true;
            }

            // 检查当前程序行号变化 (从1开始，而NC_Status_Work_Line_NO从0开始)
            uint32_t currentLine = m_ncChannelOutPtrArr[i].m_shm32Ptr->NC_Status_Work_Line_NO + 1;
            if (newStatus.currentLine != currentLine) {
                HW_LOG_DEBUG(m_logger, "通道 %zu 当前程序行号变化: %d -> %d", i, newStatus.currentLine, currentLine);
                newStatus.currentLine = currentLine;
                hasChanged = true;
            }

            // 检查程序总行数变化
            uint32_t totalLines = m_ncChannelOutPtrArr[i].m_shm32Ptr->NC_FileLength;
            if (newStatus.totalLines != totalLines) {
                HW_LOG_DEBUG(m_logger, "通道 %zu 程序总行数变化: %d -> %d", i, newStatus.totalLines, totalLines);
                newStatus.totalLines = totalLines;
                hasChanged = true;
            }

            // 检查循环计数变化
            // 暂时设置为0，待确认正确的字段名
            int32_t loopCounter = 0;
            if (newStatus.loopCounter != loopCounter) {
                HW_LOG_DEBUG(m_logger, "通道 %zu 循环计数变化: %d -> %d", i, newStatus.loopCounter, loopCounter);
                newStatus.loopCounter = loopCounter;
                hasChanged = true;
            }

            // 检查程序运行状态变化
            bool isRunning = (state == CncSystemState::Running);
            bool isPaused = (state == CncSystemState::Paused);
            bool isError = (state == CncSystemState::Error);

            if (newStatus.isRunning != isRunning || newStatus.isPaused != isPaused || newStatus.isError != isError) {
                HW_LOG_DEBUG(m_logger, "通道 %zu 程序状态变化: 运行(%d->%d) 暂停(%d->%d) 错误(%d->%d)", i,
                             newStatus.isRunning, isRunning, newStatus.isPaused, isPaused, newStatus.isError, isError);
                newStatus.isRunning = isRunning;
                newStatus.isPaused = isPaused;
                newStatus.isError = isError;
                hasChanged = true;
            }

            // 检查程序控制标志位变化
            uint16_t isOptionalStopActive = 0;
            getPlcDataYBit(4, 3, isOptionalStopActive);

            uint16_t isBlockSkipActive = 0;
            getPlcDataYBit(4, 5, isBlockSkipActive);

            uint16_t isHandwheelOffsetActive = 0;
            getPlcDataYBit(4, 8, isHandwheelOffsetActive);

            uint16_t currentSingleBlockMode = 0;
            SingleBlockModeType mode = SingleBlockModeType::Off;
            getPlcDataYBit(4, 2, currentSingleBlockMode);
            if (currentSingleBlockMode != 0) {
                mode = SingleBlockModeType::SB3_Precise;
            }

            if (newStatus.isOptionalStopActive != (isOptionalStopActive != 0) ||
                newStatus.isBlockSkipActive != (isBlockSkipActive != 0) ||
                newStatus.isHandwheelOffsetActive != (isHandwheelOffsetActive != 0) ||
                newStatus.currentSingleBlockMode != mode) {
                HW_LOG_DEBUG(m_logger, "通道 %zu 程序控制标志位变化", i);
                newStatus.isOptionalStopActive = (isOptionalStopActive != 0);
                newStatus.isBlockSkipActive = (isBlockSkipActive != 0);
                newStatus.isHandwheelOffsetActive = (isHandwheelOffsetActive != 0);
                newStatus.currentSingleBlockMode = mode;
                hasChanged = true;
            }

            // 如果有变化，触发事件通知
            if (hasChanged) {
                m_chanProgramStatusArr[i] = newStatus;

                HW_LOG_DEBUG(m_logger, "通道 %zu, newStatus.currentProgramNo=%d", i, newStatus.currentProgramNo);

                ChannelProgramStatusEventPayload payload;
                payload.channelId = i;
                payload.reason = ChannelProgramStatusEventPayload::ChangeReason::LineChanged;
                payload.newStatus = newStatus;

                // if (!m_ignoreProgramStatusChangedEvent) {
                CncEvent event = {CncEventType::ProgramStatusChanged, payload};
                notifyEventListeners(event);
                HW_LOG_DEBUG(m_logger,
                             "通道 %zu 程序状态变化: 程序号=%d, 嵌套层级=%d, 主程序行号=%d, 当前行号=%d, 总行数=%d", i,
                             newStatus.currentProgramNo, newStatus.nestingLevel, newStatus.currentBlock,
                             newStatus.currentLine, newStatus.totalLines);
                // }
            }
        }
    }
}

void GoogolCncInterface::checkMdiResultEvent() {}
void GoogolCncInterface::checkFeedOverrideChangedEvent() {}
void GoogolCncInterface::checkRapidOverrideChangedEvent() {}
void GoogolCncInterface::checkSpindleOverrideChangedEvent() {}
void GoogolCncInterface::checkToolInSpindleChangedEvent() {}
void GoogolCncInterface::checkActiveWorkOffsetChangedEvent() {}
void GoogolCncInterface::checkHomingProcessUpdateEvent() {}
void GoogolCncInterface::checkAxisHomedStateChangedEvent() {}

void GoogolCncInterface::checkSystemStateEvent() {
    // if (m_ignoreProgramStatusChangedEvent) {
    // HW_LOG_DEBUG(m_logger, "ignore checkSystemStateEvent");
    // return;
    // }

    unsigned short f_value = 0;

    getFPara(2, 1, &f_value);
    unsigned short startBit = f_value & (1 << 2) | f_value & (1 << 6) | f_value & (1 << 10);
    unsigned short pauseBit = f_value & (1 << 1) | f_value & (1 << 5) | f_value & (1 << 9);
    unsigned short idleBit = f_value & (1 << 0) | f_value & (1 << 4) | f_value & (1 << 8);

    // idleBit 和 stopBit 总是有值
    bool isNotify = false;
    if (startBit != 0 && m_currentSysStatusArr[0] != CncSystemState::Running) {
        isNotify = true;
        m_currentSysStatusArr[0] = CncSystemState::Running;
    } else if (pauseBit != 0 && m_currentSysStatusArr[0] != CncSystemState::Paused) {
        isNotify = true;
        m_currentSysStatusArr[0] = CncSystemState::Paused;
    }

    if (startBit == 0 && pauseBit == 0 && m_currentSysStatusArr[0] != CncSystemState::Idle) {
        isNotify = true;
        m_currentSysStatusArr[0] = CncSystemState::Idle;
    }

    if (isNotify) {
        SystemStateEventPayload payload = {m_currentSysStatusArr[0]};
        CncEvent event = {CncEventType::SystemStateChanged, payload};
        notifyEventListeners(event);
    }
}

void GoogolCncInterface::checkOperatingModeEvent() {
    OperatingMode curOpMode = OperatingMode::Auto;
    ErrorCode ret = getOperatingMode(0, curOpMode);
    if (ret == ErrorCode::Success && m_opMode != curOpMode) {
        m_opMode = curOpMode;
        ChannelOperatingModeEventPayload payload = {0, m_opMode};
        CncEvent event = {CncEventType::OperatingModeChanged, payload};
        notifyEventListeners(event);
    }
}

void GoogolCncInterface::checkPowerOffEvent() {
    unsigned short flag = 0;
    getPlcDataYBit(41, 1, flag);
    if (flag == 1 && !m_isPowerOffing) {
        m_isPowerOffing = true;
        CncEvent event = {CncEventType::PowerOff};
        notifyEventListeners(event);
    }
    if (flag == 0) {
        m_isPowerOffing = false;
    }
}

void GoogolCncInterface::checkMsgAndLog() {
    char logInfo[512] = {0};
    short ret = GTC_GetLogInfoAndRemove(logInfo);
    if (ret == 0) {
        HW_LOG_DEBUG(m_logger, "[LOG] %s", logInfo);
    }

    memset(logInfo, sizeof(logInfo), 1);
    ret = GTC_GetMsgInfoAndRemove(logInfo);
    if (ret == 0) {
        HW_LOG_DEBUG(m_logger, "[MSG] %s", logInfo);
    }
}

void GoogolCncInterface::eventThreadLoop() {
    HW_LOG_DEBUG(m_logger, "EventWorkerThread 启动");
    if (pthread_setname_np(pthread_self(), "EventWorker") != 0) {
        HW_LOG_ERROR(m_logger, "设置EventWorker名称失败");
    }

    int policy = SCHED_FIFO;
    sched_param sched;
    sched.sched_priority = 10;
    if (pthread_setschedparam(pthread_self(), policy, &sched) != 0) {
        HW_LOG_INFO(m_logger, "设置线程优先级失败，可能需要 root 权限");
    }

    initEventFlags();

    while (!m_eventStopFlag.load()) {
        checkSystemStateEvent();
        checkOperatingModeEvent();
        checkAlarmOccurredEvent();
        checkAlarmClearedEvent();
        checkConnectionStatusEvent();
        checkProgramStatusChangedEvent();
        checkMdiResultEvent();
        checkFeedOverrideChangedEvent();
        checkRapidOverrideChangedEvent();
        checkSpindleOverrideChangedEvent();
        checkToolInSpindleChangedEvent();
        checkActiveWorkOffsetChangedEvent();
        checkHomingProcessUpdateEvent();
        checkAxisHomedStateChangedEvent();
        checkPlcKeyEvent();
        checkPowerOffEvent();
        checkMsgAndLog();

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    HW_LOG_DEBUG(m_logger, "EventWorkerThread 停止");
}

void GoogolCncInterface::debugMacroBindings() {
    HW_LOG_INFO(m_logger, "=== 开始检查宏程序绑定配置 ===");

    for (int channelId = 0; channelId < m_loadedChannelsCount && channelId < MAX_CHANNEL_NUM; ++channelId) {
        HW_LOG_INFO(m_logger, "检查通道 %d 的宏程序绑定:", channelId);

        if (!m_macroSettingParamPtr[channelId].m_shm32Ptr) {
            HW_LOG_ERROR(m_logger, "通道 %d 的宏设置参数指针为空", channelId);
            continue;
        }

        auto* macroPtr = m_macroSettingParamPtr[channelId].m_shm32Ptr;

        // 检查M代码绑定
        HW_LOG_INFO(m_logger, "--- M代码宏程序绑定 ---");
        bool foundM06 = false;
        for (int i = 0; i < MACRO_GMT_N; ++i) {
            if (macroPtr->m_nMacroMcode[i].nNcNum != 0) {
                HW_LOG_INFO(m_logger, "M%d -> 文件索引:%d, 类型:%d, 分组:%d", macroPtr->m_nMacroMcode[i].nNcNum, i,
                            macroPtr->m_nMacroMcode[i].nType, macroPtr->m_nMacroMcode[i].nGrpNo);

                if (macroPtr->m_nMacroMcode[i].nNcNum == 6) {
                    foundM06 = true;
                    HW_LOG_INFO(m_logger, "✓ 找到M06绑定配置");
                }
            }
        }

        if (!foundM06) {
            HW_LOG_WARN(m_logger, "✗ 未找到M06宏程序绑定");
        }

        // 检查T代码绑定
        HW_LOG_INFO(m_logger, "--- T代码宏程序绑定 ---");
        bool foundT = false;
        for (int i = 0; i < MACRO_GMT_N; ++i) {
            if (macroPtr->m_nMacroTcode[i].nNcNum != 0) {
                HW_LOG_INFO(m_logger, "T%d -> 文件索引:%d, 类型:%d, 分组:%d", macroPtr->m_nMacroTcode[i].nNcNum, i,
                            macroPtr->m_nMacroTcode[i].nType, macroPtr->m_nMacroTcode[i].nGrpNo);

                if (macroPtr->m_nMacroTcode[i].nNcNum == -1) {
                    foundT = true;
                    HW_LOG_INFO(m_logger, "✓ 找到T(-1)通用绑定配置");
                }
            }
        }

        if (!foundT) {
            HW_LOG_WARN(m_logger, "✗ 未找到T代码宏程序绑定");
        }

        // 检查文件名绑定
        if (m_macroSettingParamPtr[channelId].m_shm8Ptr) {
            HW_LOG_INFO(m_logger, "--- 宏程序文件名绑定 ---");
            auto* namePtr = m_macroSettingParamPtr[channelId].m_shm8Ptr;

            for (int i = 0; i < MACRO_GMT_N && i < 10; ++i) {  // 只显示前10个
                if (strlen(namePtr->m_nMacroMname[i].cName) > 0) {
                    HW_LOG_INFO(m_logger, "M宏程序[%d]: %s", i, namePtr->m_nMacroMname[i].cName);
                }
                if (strlen(namePtr->m_nMacroTname[i].cName) > 0) {
                    HW_LOG_INFO(m_logger, "T宏程序[%d]: %s", i, namePtr->m_nMacroTname[i].cName);
                }
            }
        }
    }

    HW_LOG_INFO(m_logger, "=== 宏程序绑定配置检查完成 ===");
}


ErrorCode GoogolCncInterface::validateChannel(int channelId, const std::string& operation) const {
    if (!isInitialized()) {
        m_lastError = operation + " 错误: 接口未初始化";
        return ErrorCode::NotInitialized;
    }

    if (channelId < 0 || channelId >= MAX_CHANNEL_NUM) {
        m_lastError = operation + " 错误: 无效的通道ID " + std::to_string(channelId);
        return ErrorCode::InvalidParam;
    }

    return ErrorCode::Success;
}

ErrorCode GoogolCncInterface::validateChannelAndSpindle(int channelId, int spindleIndex,
                                                        const std::string& operation) const {
    ErrorCode ec = validateChannel(channelId, operation);
    if (ec != ErrorCode::Success) return ec;

    if (spindleIndex < 0) {
        m_lastError = operation + " 错误: 无效的主轴索引 " + std::to_string(spindleIndex);
        return ErrorCode::InvalidParam;
    }

    return ErrorCode::Success;
}